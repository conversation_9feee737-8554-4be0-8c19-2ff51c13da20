import React, { useRef, useEffect } from 'react';
import { BackgroundOption } from '@/common/types';
import './index.less';

interface BackgroundSelectorProps {
  onChange: (background: BackgroundOption | null) => void;
  disabled?: boolean;
}

const BackgroundSelector: React.FC<BackgroundSelectorProps> = ({
  onChange,
  disabled = false,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  // Windows端鼠标滚轮横向滚动支持 - Windows mouse wheel horizontal scrolling support
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleWheel = (e: WheelEvent) => {
      // 检测是否为垂直滚动 - Check if it's vertical scrolling
      if (Math.abs(e.deltaY) > Math.abs(e.deltaX)) {
        e.preventDefault(); // 阻止默认垂直滚动 - Prevent default vertical scrolling

        // 将垂直滚动转换为横向滚动 - Convert vertical scroll to horizontal scroll
        const scrollAmount = e.deltaY * 0.5; // 调整滚动速度 - Adjust scroll speed
        container.scrollLeft += scrollAmount;
      }
    };

    // 添加滚轮事件监听 - Add wheel event listener
    container.addEventListener('wheel', handleWheel, { passive: false });

    return () => {
      container.removeEventListener('wheel', handleWheel);
    };
  }, []);

  // 纹理背景选项数据 - Texture background options data
  const backgroundOptions: BackgroundOption[] = [
    {
      id: 'pic1',
      imageUrl: 'https://img.alicdn.com/imgextra/i2/O1CN01VWnU5l1VkWtjSufLQ_!!6000000002691-2-tps-216-216.png',
      title: '科幻之光',
      desc: '黑色玻璃展台，球体和台阶',
    },
    {
      id: 'pic2',
      imageUrl: 'https://img.alicdn.com/imgextra/i1/O1CN01TJzn7B21yKXKtYEyG_!!6000000007053-2-tps-216-216.png',
      title: '暗台灯条',
      desc: '金属台面，垂直 LED 灯带发出明亮',
    },
    {
      id: 'pic3',
      imageUrl: 'https://img.alicdn.com/imgextra/i2/O1CN01qBBzTw1P4xFYea5J0_!!6000000001788-2-tps-216-216.png',
      title: '霓虹灯火',
      desc: '雨后沥青路、倒影、迷离、颜色、城市灯光',
    },
    {
      id: 'pic4',
      imageUrl: 'https://img.alicdn.com/imgextra/i2/O1CN01KYVxh21Q74hRdAnY5_!!6000000001928-2-tps-216-216.png',
      title: '明亮木台',
      desc: '木质书桌，方形和台阶，亮色调',
    },
    {
      id: 'pic5',
      imageUrl: 'https://img.alicdn.com/imgextra/i3/O1CN01poht3I1RnV0D8S3gR_!!6000000002156-2-tps-216-216.png',
      title: '蓝色展台',
      desc: '展台、金色、蓝色、高级',
    },
    {
      id: 'pic6',
      imageUrl: 'https://img.alicdn.com/imgextra/i1/O1CN01RpVT781iLQBpuUoET_!!6000000004396-2-tps-216-216.png',
      title: '镶金岩石',
      desc: '金属台面深色纹理岩石，以黑色为主略带金色点缀，垂直 LED 灯带发出明亮',
    },
    {
      id: 'pic7',
      imageUrl: 'https://img.alicdn.com/imgextra/i4/O1CN01wDB7D01FkV1FxRge0_!!6000000000525-2-tps-216-216.png',
      title: '白色空间',
      desc: '白色空间、背景光效、明亮环境',
    },
  ];

  // Handle background selection - 处理背景选择
  const handleBackgroundSelect = (option: BackgroundOption) => {
    onChange(option);
  };

  // Render background preview - 渲染背景预览
  const renderBackgroundPreview = (option: BackgroundOption) => {
    return (
      <div
        key={option.id}
        className={`background-option ${disabled ? 'disabled' : ''}`}
        onClick={() => !disabled && handleBackgroundSelect(option)}
      >
        <div
          className="background-preview"
          style={{
            backgroundImage: `url(${option.imageUrl})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        />
      </div>
    );
  };

  return (
    <div
      ref={containerRef}
      className={`background-selector ${disabled ? 'disabled' : ''}`}
    >
      {/* 纹理背景选项 - Texture background options */}
      {backgroundOptions.map(renderBackgroundPreview)}
    </div>
  );
};

export default BackgroundSelector;
